#!/usr/bin/env python
"""
Script de démonstration de l'application de gestion de coupons.
Ce script génère des coupons de test et démontre les fonctionnalités.
"""

import os
import sys
import django
from django.test import Client
from django.urls import reverse
import json

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'coupon_management.settings')
django.setup()

from coupons.models import Coupon
from coupons.views import generate_coupons_logic


def demo_generation():
    """Démonstration de la génération de coupons"""
    print("🎫 DÉMONSTRATION - Génération de Coupons")
    print("=" * 50)
    
    # Nettoyer la base de données
    Coupon.objects.all().delete()
    print("🧹 Base de données nettoyée")
    
    # Générer une page de coupons (431201-431220)
    print("\n📄 Génération d'une page (20 coupons):")
    coupons_data = generate_coupons_logic(431201, 431220)
    
    # <PERSON>uvegarder en base
    for coupon_data in coupons_data:
        Coupon.objects.create(
            serial_number=coupon_data['serial_number'],
            valeur=coupon_data['valeur'],
            suffixe=coupon_data['suffixe']
        )
    
    print(f"✅ {len(coupons_data)} coupons générés")
    print(f"💰 Total: {sum(c['valeur'] for c in coupons_data)} DH")
    
    # Afficher quelques exemples
    print("\n🔍 Exemples de coupons générés:")
    for i, coupon in enumerate(coupons_data[:5]):
        print(f"  {i+1}. {coupon['serial_number']} - {coupon['valeur']} DH")
    print("  ...")
    
    return len(coupons_data)


def demo_calculation_methods():
    """Démonstration des deux méthodes de calcul"""
    print("\n💰 DÉMONSTRATION - Méthodes de Calcul")
    print("=" * 50)
    
    client = Client()
    
    # Méthode 1: Calcul algorithmique (génération à la volée)
    print("\n🧮 Méthode 1: Calcul algorithmique")
    response = client.post(reverse('coupons:calculate'), {
        'start_serial': 431201,
        'end_serial': 431210
    })
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Plage: {data['start_serial']}-{data['end_serial']}")
        print(f"📊 Nombre: {data['count']} coupons")
        print(f"💰 Total: {data['total_amount']} DH")
    else:
        print("❌ Erreur dans le calcul algorithmique")
    
    # Méthode 2: Calcul basé sur JSON/DB (comme Tkinter)
    print("\n📊 Méthode 2: Calcul basé sur la base de données")
    response = client.post(reverse('coupons:calculate_json'), {
        'start_serial': 'C2500020AG431201',
        'end_serial': 'C2500100AG431210'
    })
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Plage suffixes: {data['start_suffix']}-{data['end_suffix']}")
        print(f"📊 Nombre: {data['count']} coupons")
        print(f"💰 Total: {data['total_amount']} DH")
        print(f"🔧 Méthode: {data['method']}")
    else:
        print("❌ Erreur dans le calcul basé sur JSON")


def demo_export():
    """Démonstration de l'export JSON"""
    print("\n📁 DÉMONSTRATION - Export JSON")
    print("=" * 50)
    
    client = Client()
    
    response = client.post(reverse('coupons:export'), {
        'start_serial': 431201,
        'end_serial': 431205
    })
    
    if response.status_code == 200:
        # Sauvegarder le fichier
        with open('demo_export.json', 'wb') as f:
            f.write(response.content)
        
        # Charger et afficher un aperçu
        with open('demo_export.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✅ Fichier exporté: demo_export.json")
        print(f"📊 Métadonnées:")
        print(f"  - Plage: {data['metadata']['start_serial']}-{data['metadata']['end_serial']}")
        print(f"  - Total coupons: {data['metadata']['total_coupons']}")
        print(f"  - Montant total: {data['metadata']['total_amount']} DH")
        
        print(f"\n🔍 Premiers coupons exportés:")
        for i, coupon in enumerate(data['coupons'][:3]):
            print(f"  {i+1}. {coupon['serial_number']} - {coupon['valeur']} DH")
    else:
        print("❌ Erreur lors de l'export")


def demo_comparison_with_tkinter():
    """Comparaison avec la logique Tkinter"""
    print("\n🔄 DÉMONSTRATION - Comparaison avec Tkinter")
    print("=" * 50)
    
    # Simuler la logique de votre code Tkinter
    def tkinter_logic(start_serial, end_serial):
        pattern = [
            20, 50, 100, 200,
            20, 50, 100, 200,
            20, 50, 100, 200,
            20, 50, 100, 500,
            20, 100, 100, 500
        ]
        
        def get_suffix(serial):
            return int(str(serial)[-6:])
        
        start_suffix = get_suffix(start_serial)
        end_suffix = get_suffix(end_serial)
        total_coupons = end_suffix - start_suffix + 1
        
        coupons = []
        current_suffix = start_suffix
        
        for i in range(total_coupons):
            value = pattern[i % len(pattern)]
            value_str = f"{value:03d}"
            suffix_str = f"{current_suffix:06d}"
            serial = f"C2500{value_str}AG{suffix_str}"
            
            coupons.append({
                "serial": serial,
                "value": value,
                "suffix": int(suffix_str)
            })
            
            current_suffix += 1
        
        return coupons
    
    # Test avec la même plage
    start, end = 431201, 431220
    
    # Logique Django
    django_coupons = generate_coupons_logic(start, end)
    django_total = sum(c['valeur'] for c in django_coupons)
    
    # Logique Tkinter simulée
    tkinter_coupons = tkinter_logic(start, end)
    tkinter_total = sum(c['value'] for c in tkinter_coupons)
    
    print(f"📊 Comparaison pour la plage {start}-{end}:")
    print(f"  Django  : {len(django_coupons)} coupons, {django_total} DH")
    print(f"  Tkinter : {len(tkinter_coupons)} coupons, {tkinter_total} DH")
    
    if django_total == tkinter_total:
        print("✅ Les deux méthodes donnent le même résultat !")
    else:
        print("❌ Différence détectée entre les méthodes")
    
    # Vérifier quelques coupons individuels
    print("\n🔍 Vérification des premiers coupons:")
    for i in range(min(5, len(django_coupons))):
        d_coupon = django_coupons[i]
        t_coupon = tkinter_coupons[i]
        match = "✅" if d_coupon['valeur'] == t_coupon['value'] else "❌"
        print(f"  {i+1}. Django: {d_coupon['valeur']} DH, Tkinter: {t_coupon['value']} DH {match}")


def main():
    """Fonction principale de démonstration"""
    print("🚀 DÉMONSTRATION COMPLÈTE - Application de Gestion de Coupons")
    print("=" * 70)
    print("Cette démonstration montre toutes les fonctionnalités de l'application")
    print("et compare avec votre implémentation Tkinter existante.")
    print()
    
    try:
        # 1. Génération
        demo_generation()
        
        # 2. Calculs
        demo_calculation_methods()
        
        # 3. Export
        demo_export()
        
        # 4. Comparaison
        demo_comparison_with_tkinter()
        
        print("\n" + "=" * 70)
        print("🎉 DÉMONSTRATION TERMINÉE AVEC SUCCÈS !")
        print("🌐 Vous pouvez maintenant tester l'interface web à: http://127.0.0.1:8000/")
        print("🔧 L'application Django reproduit fidèlement votre logique Tkinter.")
        
    except Exception as e:
        print(f"\n❌ Erreur pendant la démonstration: {str(e)}")
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
