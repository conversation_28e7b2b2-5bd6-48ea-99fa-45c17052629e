from django.db import models
import json


class Coupon(models.Model):
    """
    Modèle pour représenter un coupon financier.
    Format: C2500[VAL]AG[SUFFIXE]
    """
    VALEUR_CHOICES = [
        (20, '020'),
        (50, '050'),
        (100, '100'),
        (200, '200'),
        (500, '500'),
    ]

    serial_number = models.CharField(max_length=20, unique=True, verbose_name="Numéro de série")
    valeur = models.IntegerField(choices=VALEUR_CHOICES, verbose_name="Valeur en DH")
    suffixe = models.CharField(max_length=6, verbose_name="Suffixe")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="Date de création")

    class Meta:
        verbose_name = "Coupon"
        verbose_name_plural = "Coupons"
        ordering = ['suffixe']

    def __str__(self):
        return f"{self.serial_number} - {self.valeur} DH"

    @property
    def formatted_serial(self):
        """Retourne le numéro de série formaté selon le modèle C2500[VAL]AG[SUFFIXE]"""
        valeur_str = f"{self.valeur:03d}"
        return f"C2500{valeur_str}AG{self.suffixe}"

    def to_dict(self):
        """Convertit le coupon en dictionnaire pour l'export JSON"""
        return {
            'serial_number': self.formatted_serial,
            'valeur': self.valeur,
            'suffixe': self.suffixe,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
