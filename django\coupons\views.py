from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib import messages
from .models import Coupon
import json


def index(request):
    """Vue principale de l'application"""
    return render(request, 'coupons/index.html')


def get_suffix_from_serial(serial):
    """Extrait le suffixe (6 derniers chiffres) d'un numéro de série complet"""
    try:
        return int(str(serial)[-6:])
    except (ValueError, TypeError):
        return int(serial)  # Si c'est déjà un entier


def generate_coupons_logic_from_serials(start_serial, end_serial):
    """
    Logique de génération basée sur les numéros de série complets (comme Tkinter).
    Reproduit exactement votre logique Tkinter.
    """
    # Modèle d'une page (20 coupons) - Total: 2500 DH
    pattern = [
        20, 50, 100, 200,
        20, 50, 100, 200,
        20, 50, 100, 200,
        20, 50, 100, 500,
        20, 100, 100, 500
    ]

    # Extraire les suffixes (comme dans votre code Tkinter)
    start_suffix = get_suffix_from_serial(start_serial)
    end_suffix = get_suffix_from_serial(end_serial)

    total_coupons = end_suffix - start_suffix + 1

    coupons = []
    current_suffix = start_suffix

    for i in range(total_coupons):
        # IMPORTANT: Position absolue dans le pattern répétitif
        # Chaque suffixe a sa position fixe dans le cycle de 20
        position_in_pattern = current_suffix % len(pattern)
        value = pattern[position_in_pattern]

        value_str = f"{value:03d}"  # Toujours 3 chiffres
        suffix_str = f"{current_suffix:06d}"
        serial = f"C2500{value_str}AG{suffix_str}"

        coupons.append({
            "serial_number": serial,
            "valeur": value,
            "suffixe": suffix_str
        })

        current_suffix += 1

    return coupons


def generate_coupons_logic(start_suffix, end_suffix):
    """Wrapper pour compatibilité avec l'ancienne interface"""
    return generate_coupons_logic_from_serials(start_suffix, end_suffix)


def generate_coupons(request):
    """Vue pour générer les coupons"""
    if request.method == 'POST':
        try:
            start_serial = int(request.POST.get('start_serial', 0))
            end_serial = int(request.POST.get('end_serial', 0))

            if start_serial <= 0 or end_serial <= 0 or start_serial > end_serial:
                messages.error(request, "Veuillez saisir des numéros de série valides.")
                return render(request, 'coupons/index.html')

            # Validation basique (pas de restriction sur le multiple de 20 pour plus de flexibilité)
            total_coupons = end_serial - start_serial + 1
            if total_coupons > 1000:  # Limite raisonnable pour éviter les erreurs
                messages.error(request, f"❌ Trop de coupons demandés ({total_coupons}). Maximum: 1000 coupons.")
                return render(request, 'coupons/index.html')

            # Générer les coupons avec la nouvelle logique
            coupons_data = generate_coupons_logic_from_serials(start_serial, end_serial)

            # Sauvegarder en base de données
            created_coupons = []
            for coupon_data in coupons_data:
                coupon, created = Coupon.objects.get_or_create(
                    serial_number=coupon_data['serial_number'],
                    defaults={
                        'valeur': coupon_data['valeur'],
                        'suffixe': coupon_data['suffixe']
                    }
                )
                if created:
                    created_coupons.append(coupon)

            messages.success(request, f"{len(created_coupons)} coupons générés avec succès.")

            context = {
                'coupons': created_coupons,
                'total_amount': sum(c.valeur for c in created_coupons),
                'start_serial': start_serial,
                'end_serial': end_serial
            }
            return render(request, 'coupons/results.html', context)

        except ValueError:
            messages.error(request, "Veuillez saisir des numéros valides.")
            return render(request, 'coupons/index.html')

    return render(request, 'coupons/index.html')


def calculate_total(request):
    """
    Vue pour calculer le total des coupons dans une plage.
    IMPORTANT: Calcule basé sur les coupons EXISTANTS en base de données,
    pas sur le pattern théorique (comme votre logique Tkinter).
    """
    if request.method == 'POST':
        try:
            start_serial = int(request.POST.get('start_serial', 0))
            end_serial = int(request.POST.get('end_serial', 0))

            if start_serial <= 0 or end_serial <= 0 or start_serial > end_serial:
                return JsonResponse({'error': 'Numéros de série invalides'}, status=400)

            # CORRECTION: Chercher dans les coupons EXISTANTS en base de données
            # Extraire les suffixes de la plage demandée
            start_suffix = get_suffix_from_serial(start_serial)
            end_suffix = get_suffix_from_serial(end_serial)

            # Filtrer les coupons existants dans cette plage
            existing_coupons = Coupon.objects.filter(
                suffixe__gte=f"{start_suffix:06d}",
                suffixe__lte=f"{end_suffix:06d}"
            ).order_by('suffixe')

            if not existing_coupons.exists():
                return JsonResponse({
                    'error': f'Aucun coupon trouvé dans la plage {start_suffix}-{end_suffix}. Générez d\'abord les coupons.',
                    'suggestion': 'Cliquez sur "Générer Coupons" avant de calculer.'
                }, status=404)

            # Calculer le total des coupons existants
            total_amount = sum(coupon.valeur for coupon in existing_coupons)

            return JsonResponse({
                'total_amount': total_amount,
                'count': existing_coupons.count(),
                'start_serial': start_serial,
                'end_serial': end_serial,
                'method': 'existing_coupons',
                'found_coupons': [
                    {
                        'suffixe': coupon.suffixe,
                        'valeur': coupon.valeur,
                        'serial': coupon.formatted_serial
                    } for coupon in existing_coupons
                ]
            })

        except ValueError:
            return JsonResponse({'error': 'Veuillez saisir des numéros valides'}, status=400)

    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)


def export_coupons_json(request):
    """Vue pour exporter les coupons en JSON"""
    if request.method == 'POST':
        try:
            start_serial = int(request.POST.get('start_serial', 0))
            end_serial = int(request.POST.get('end_serial', 0))

            if start_serial <= 0 or end_serial <= 0 or start_serial > end_serial:
                return JsonResponse({'error': 'Numéros de série invalides'}, status=400)

            # Générer les coupons
            coupons_data = generate_coupons_logic_from_serials(start_serial, end_serial)

            # Préparer les données pour l'export
            export_data = {
                'metadata': {
                    'start_serial': start_serial,
                    'end_serial': end_serial,
                    'total_coupons': len(coupons_data),
                    'total_amount': sum(coupon['valeur'] for coupon in coupons_data),
                    'generated_at': json.dumps(None)  # Sera remplacé par la date actuelle
                },
                'coupons': coupons_data
            }

            # Créer la réponse HTTP avec le fichier JSON
            response = HttpResponse(
                json.dumps(export_data, indent=2, ensure_ascii=False),
                content_type='application/json'
            )
            response['Content-Disposition'] = 'attachment; filename="coupons.json"'

            return response

        except ValueError:
            return JsonResponse({'error': 'Veuillez saisir des numéros valides'}, status=400)

    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)


def calculate_from_json(request):
    """
    Vue pour calculer le total à partir d'un fichier JSON existant
    (comme dans votre implémentation Tkinter)
    """
    if request.method == 'POST':
        try:
            start_serial = request.POST.get('start_serial', '').strip()
            end_serial = request.POST.get('end_serial', '').strip()

            if not start_serial or not end_serial:
                return JsonResponse({'error': 'Numéros de série requis'}, status=400)

            # Extraire les suffixes (comme dans votre code Tkinter)
            def get_suffix(serial):
                return int(serial[-6:])

            def extract_value(serial):
                try:
                    return int(serial[5:8])  # Positions 5,6,7 comme dans votre code
                except ValueError:
                    return 0

            try:
                start_suffix = get_suffix(start_serial)
                end_suffix = get_suffix(end_serial)
            except ValueError:
                return JsonResponse({'error': 'Format de numéro de série invalide'}, status=400)

            if start_suffix > end_suffix:
                return JsonResponse({'error': 'Le numéro de début doit être inférieur au numéro de fin'}, status=400)

            # Charger les coupons depuis la base de données (équivalent de votre JSON)
            all_coupons = Coupon.objects.all()

            # Filtrer les coupons valides (comme dans votre logique Tkinter)
            valid_coupons = []
            for coupon in all_coupons:
                coupon_suffix = int(coupon.suffixe)
                if start_suffix <= coupon_suffix <= end_suffix:
                    valid_coupons.append(coupon)

            total_amount = sum(coupon.valeur for coupon in valid_coupons)

            return JsonResponse({
                'total_amount': total_amount,
                'count': len(valid_coupons),
                'start_suffix': start_suffix,
                'end_suffix': end_suffix,
                'method': 'json_based'
            })

        except Exception as e:
            return JsonResponse({'error': f'Erreur: {str(e)}'}, status=400)

    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
