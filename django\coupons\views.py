from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib import messages
from .models import Coupon
import json


def index(request):
    """Vue principale de l'application"""
    return render(request, 'coupons/index.html')


def generate_coupons_logic(start_suffix, end_suffix):
    """
    Logique de génération des coupons selon le modèle défini.
    Une page contient 20 coupons dans l'ordre spécifique.
    """
    # Modèle d'une page (20 coupons) - Total: 2500 DH
    page_pattern = [
        20, 50, 100, 200,  # Ligne 1
        20, 50, 100, 200,  # Ligne 2
        20, 50, 100, 200,  # Ligne 3
        20, 50, 100, 500,  # Ligne 4
        20, 100, 100, 500  # Ligne 5
    ]

    coupons = []
    current_suffix = start_suffix

    while current_suffix <= end_suffix:
        # Calculer la position dans la page (0-19)
        position_in_page = (current_suffix - start_suffix) % 20
        valeur = page_pattern[position_in_page]

        # <PERSON><PERSON><PERSON> le coupon
        coupon_data = {
            'suffixe': f"{current_suffix:06d}",
            'valeur': valeur,
            'serial_number': f"C2500{valeur:03d}AG{current_suffix:06d}"
        }
        coupons.append(coupon_data)
        current_suffix += 1

    return coupons


def generate_coupons(request):
    """Vue pour générer les coupons"""
    if request.method == 'POST':
        try:
            start_serial = int(request.POST.get('start_serial', 0))
            end_serial = int(request.POST.get('end_serial', 0))

            if start_serial <= 0 or end_serial <= 0 or start_serial > end_serial:
                messages.error(request, "Veuillez saisir des numéros de série valides.")
                return render(request, 'coupons/index.html')

            # Vérifier que le nombre total est un multiple de 20 (comme dans votre cahier des charges)
            total_coupons = end_serial - start_serial + 1
            if total_coupons % 20 != 0:
                messages.error(request, f"❌ Le nombre total de coupons ({total_coupons}) doit être un multiple de 20 (une page = 20 coupons).")
                return render(request, 'coupons/index.html')

            # Générer les coupons
            coupons_data = generate_coupons_logic(start_serial, end_serial)

            # Sauvegarder en base de données
            created_coupons = []
            for coupon_data in coupons_data:
                coupon, created = Coupon.objects.get_or_create(
                    serial_number=coupon_data['serial_number'],
                    defaults={
                        'valeur': coupon_data['valeur'],
                        'suffixe': coupon_data['suffixe']
                    }
                )
                if created:
                    created_coupons.append(coupon)

            messages.success(request, f"{len(created_coupons)} coupons générés avec succès.")

            context = {
                'coupons': created_coupons,
                'total_amount': sum(c.valeur for c in created_coupons),
                'start_serial': start_serial,
                'end_serial': end_serial
            }
            return render(request, 'coupons/results.html', context)

        except ValueError:
            messages.error(request, "Veuillez saisir des numéros valides.")
            return render(request, 'coupons/index.html')

    return render(request, 'coupons/index.html')


def calculate_total(request):
    """Vue pour calculer le total des coupons dans une plage"""
    if request.method == 'POST':
        try:
            start_serial = int(request.POST.get('start_serial', 0))
            end_serial = int(request.POST.get('end_serial', 0))

            if start_serial <= 0 or end_serial <= 0 or start_serial > end_serial:
                return JsonResponse({'error': 'Numéros de série invalides'}, status=400)

            # Générer les coupons pour calculer le total
            coupons_data = generate_coupons_logic(start_serial, end_serial)
            total_amount = sum(coupon['valeur'] for coupon in coupons_data)

            return JsonResponse({
                'total_amount': total_amount,
                'count': len(coupons_data),
                'start_serial': start_serial,
                'end_serial': end_serial
            })

        except ValueError:
            return JsonResponse({'error': 'Veuillez saisir des numéros valides'}, status=400)

    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)


def export_coupons_json(request):
    """Vue pour exporter les coupons en JSON"""
    if request.method == 'POST':
        try:
            start_serial = int(request.POST.get('start_serial', 0))
            end_serial = int(request.POST.get('end_serial', 0))

            if start_serial <= 0 or end_serial <= 0 or start_serial > end_serial:
                return JsonResponse({'error': 'Numéros de série invalides'}, status=400)

            # Générer les coupons
            coupons_data = generate_coupons_logic(start_serial, end_serial)

            # Préparer les données pour l'export
            export_data = {
                'metadata': {
                    'start_serial': start_serial,
                    'end_serial': end_serial,
                    'total_coupons': len(coupons_data),
                    'total_amount': sum(coupon['valeur'] for coupon in coupons_data),
                    'generated_at': json.dumps(None)  # Sera remplacé par la date actuelle
                },
                'coupons': coupons_data
            }

            # Créer la réponse HTTP avec le fichier JSON
            response = HttpResponse(
                json.dumps(export_data, indent=2, ensure_ascii=False),
                content_type='application/json'
            )
            response['Content-Disposition'] = 'attachment; filename="coupons.json"'

            return response

        except ValueError:
            return JsonResponse({'error': 'Veuillez saisir des numéros valides'}, status=400)

    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)


def calculate_from_json(request):
    """
    Vue pour calculer le total à partir d'un fichier JSON existant
    (comme dans votre implémentation Tkinter)
    """
    if request.method == 'POST':
        try:
            start_serial = request.POST.get('start_serial', '').strip()
            end_serial = request.POST.get('end_serial', '').strip()

            if not start_serial or not end_serial:
                return JsonResponse({'error': 'Numéros de série requis'}, status=400)

            # Extraire les suffixes (comme dans votre code Tkinter)
            def get_suffix(serial):
                return int(serial[-6:])

            def extract_value(serial):
                try:
                    return int(serial[5:8])  # Positions 5,6,7 comme dans votre code
                except ValueError:
                    return 0

            try:
                start_suffix = get_suffix(start_serial)
                end_suffix = get_suffix(end_serial)
            except ValueError:
                return JsonResponse({'error': 'Format de numéro de série invalide'}, status=400)

            if start_suffix > end_suffix:
                return JsonResponse({'error': 'Le numéro de début doit être inférieur au numéro de fin'}, status=400)

            # Charger les coupons depuis la base de données (équivalent de votre JSON)
            all_coupons = Coupon.objects.all()

            # Filtrer les coupons valides (comme dans votre logique Tkinter)
            valid_coupons = []
            for coupon in all_coupons:
                coupon_suffix = int(coupon.suffixe)
                if start_suffix <= coupon_suffix <= end_suffix:
                    valid_coupons.append(coupon)

            total_amount = sum(coupon.valeur for coupon in valid_coupons)

            return JsonResponse({
                'total_amount': total_amount,
                'count': len(valid_coupons),
                'start_suffix': start_suffix,
                'end_suffix': end_suffix,
                'method': 'json_based'
            })

        except Exception as e:
            return JsonResponse({'error': f'Erreur: {str(e)}'}, status=400)

    return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
