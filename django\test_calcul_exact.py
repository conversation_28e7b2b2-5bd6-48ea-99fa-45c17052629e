#!/usr/bin/env python
"""
Test du calcul exact selon votre exemple :
- Généré de 438921 à 438940
- Calculer de 438924 à 438929
"""

import os
import sys
import django

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'coupon_management.settings')
django.setup()

from coupons.views import generate_coupons_logic_from_serials


def test_votre_exemple():
    """Test avec votre exemple exact"""
    print("🧪 TEST CALCUL EXACT - Votre Exemple")
    print("=" * 50)
    
    # 1. Générer de 438921 à 438940 (comme vous avez fait)
    print("📊 1. Génération complète (438921 à 438940):")
    coupons_complets = generate_coupons_logic_from_serials(438921, 438940)
    total_complet = sum(c['valeur'] for c in coupons_complets)
    print(f"   Nombre de coupons: {len(coupons_complets)}")
    print(f"   Total: {total_complet} DH")
    
    # Afficher tous les coupons générés
    print("\n🔍 Détail des coupons générés:")
    for i, coupon in enumerate(coupons_complets):
        suffixe = int(coupon['suffixe'])
        print(f"   {suffixe}: {coupon['serial_number']} - {coupon['valeur']} DH")
    
    # 2. Calculer seulement de 438924 à 438929
    print(f"\n💰 2. Calcul partiel (438924 à 438929):")
    coupons_partiels = generate_coupons_logic_from_serials(438924, 438929)
    total_partiel = sum(c['valeur'] for c in coupons_partiels)
    print(f"   Nombre de coupons: {len(coupons_partiels)}")
    print(f"   Total: {total_partiel} DH")
    
    # Afficher les coupons partiels
    print("\n🔍 Détail des coupons dans la plage 438924-438929:")
    for coupon in coupons_partiels:
        suffixe = int(coupon['suffixe'])
        print(f"   {suffixe}: {coupon['serial_number']} - {coupon['valeur']} DH")
    
    # 3. Vérification manuelle
    print(f"\n✅ 3. Vérification manuelle:")
    
    # Extraire manuellement les coupons 438924-438929 de la liste complète
    coupons_manuels = []
    for coupon in coupons_complets:
        suffixe = int(coupon['suffixe'])
        if 438924 <= suffixe <= 438929:
            coupons_manuels.append(coupon)
    
    total_manuel = sum(c['valeur'] for c in coupons_manuels)
    print(f"   Extraction manuelle: {total_manuel} DH")
    
    # Comparaison
    if total_partiel == total_manuel:
        print("   ✅ SUCCÈS: Les deux méthodes donnent le même résultat!")
    else:
        print("   ❌ ERREUR: Différence entre les méthodes!")
        print(f"      Calcul direct: {total_partiel} DH")
        print(f"      Extraction manuelle: {total_manuel} DH")
    
    return total_partiel == total_manuel


def test_pattern_verification():
    """Vérifier que le pattern se répète correctement"""
    print(f"\n🔄 TEST PATTERN - Vérification du cycle de 20")
    print("=" * 50)
    
    # Pattern attendu
    pattern_attendu = [20, 50, 100, 200, 20, 50, 100, 200, 20, 50, 100, 200, 20, 50, 100, 500, 20, 100, 100, 500]
    
    # Tester plusieurs positions pour vérifier le cycle
    positions_test = [438921, 438941, 438961, 438981]  # Positions espacées de 20
    
    for pos in positions_test:
        coupon = generate_coupons_logic_from_serials(pos, pos)[0]
        position_dans_pattern = pos % 20
        valeur_attendue = pattern_attendu[position_dans_pattern]
        
        print(f"   Position {pos}: attendu {valeur_attendue} DH, obtenu {coupon['valeur']} DH", end="")
        if coupon['valeur'] == valeur_attendue:
            print(" ✅")
        else:
            print(" ❌")


def test_cas_limites():
    """Test des cas limites"""
    print(f"\n🎯 TEST CAS LIMITES")
    print("=" * 50)
    
    # Test 1: Un seul coupon
    print("📌 Test 1: Un seul coupon (438925)")
    coupon_unique = generate_coupons_logic_from_serials(438925, 438925)
    print(f"   Résultat: {coupon_unique[0]['valeur']} DH")
    
    # Test 2: Plage qui traverse plusieurs cycles de 20
    print("\n📌 Test 2: Plage traversant plusieurs cycles (438918 à 438942)")
    coupons_cycles = generate_coupons_logic_from_serials(438918, 438942)
    total_cycles = sum(c['valeur'] for c in coupons_cycles)
    print(f"   Nombre de coupons: {len(coupons_cycles)}")
    print(f"   Total: {total_cycles} DH")
    
    # Vérifier que ça inclut au moins un cycle complet
    if len(coupons_cycles) >= 20:
        print("   ✅ Inclut au moins un cycle complet")
    else:
        print("   ⚠️ Moins d'un cycle complet")


def main():
    """Fonction principale de test"""
    print("🔍 TEST COMPLET - Calcul Exact des Coupons")
    print("=" * 70)
    print("Ce test vérifie que le calcul fonctionne pour n'importe quelle plage.")
    print()
    
    try:
        # Test principal avec votre exemple
        success = test_votre_exemple()
        
        # Tests supplémentaires
        test_pattern_verification()
        test_cas_limites()
        
        print("\n" + "=" * 70)
        if success:
            print("🎉 TOUS LES TESTS RÉUSSIS!")
            print("✅ Le calcul fonctionne correctement pour n'importe quelle plage.")
            print("🚀 Vous pouvez maintenant utiliser l'application avec confiance.")
        else:
            print("❌ CERTAINS TESTS ONT ÉCHOUÉ!")
            print("🔧 Il faut corriger la logique de calcul.")
        
        return success
        
    except Exception as e:
        print(f"\n❌ Erreur pendant les tests: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
