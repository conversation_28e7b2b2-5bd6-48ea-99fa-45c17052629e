{% extends 'coupons/base.html' %}
{% load coupon_extras %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h3 class="mb-0">✅ Coupons Générés avec Succès</h3>
                <a href="{% url 'coupons:index' %}" class="btn btn-light">🔙 Retour</a>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>📊 Plage:</strong> {{ start_serial }} - {{ end_serial }}
                        </div>
                        <div class="col-md-3">
                            <strong>🎫 Nombre:</strong> {{ coupons|length }} coupons
                        </div>
                        <div class="col-md-3">
                            <strong>💰 Total:</strong> {{ total_amount }} DH
                        </div>
                        <div class="col-md-3">
                            <strong>📚 Pages:</strong> {{ coupons|length|pages_count }}
                        </div>
                    </div>
                </div>

                {% if coupons %}
                <div class="row">
                    {% for coupon in coupons %}
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="coupon-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <div class="serial-number">{{ coupon.formatted_serial }}</div>
                                    <small class="text-muted">Suffixe: {{ coupon.suffixe }}</small>
                                </div>
                                <div class="value-badge">{{ coupon.valeur }} DH</div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <div class="mt-4 text-center">
                    <form method="post" action="{% url 'coupons:export' %}" style="display: inline;">
                        {% csrf_token %}
                        <input type="hidden" name="start_serial" value="{{ start_serial }}">
                        <input type="hidden" name="end_serial" value="{{ end_serial }}">
                        <button type="submit" class="btn btn-warning btn-lg">
                            📁 Télécharger en JSON
                        </button>
                    </form>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <h5>⚠️ Aucun nouveau coupon généré</h5>
                    <p>Tous les coupons dans cette plage existent déjà dans la base de données.</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Statistiques détaillées -->
        {% if coupons %}
        <div class="card mt-4 shadow">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">📈 Statistiques Détaillées</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    {% regroup coupons by valeur as coupons_by_value %}
                    {% for value_group in coupons_by_value %}
                    <div class="col-md-2 text-center">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">{{ value_group.grouper }} DH</h5>
                                <p class="card-text">
                                    <strong>{{ value_group.list|length }}</strong> coupons<br>
                                    <small>{{ value_group.grouper|multiply:value_group.list|length }} DH total</small>
                                </p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
