#!/usr/bin/env python
"""
Script de test automatisé pour l'application de gestion de coupons.
Ce script teste tous les scénarios définis dans le cahier des charges.
"""

import os
import sys
import django
from django.test import Client
from django.urls import reverse

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'coupon_management.settings')
django.setup()

from coupons.models import Coupon
from coupons.views import generate_coupons_logic


def test_scenario_1_page():
    """Test : Génération d'une page (20 coupons = 2500 DH)"""
    print("🧪 Test 1 : Génération d'une page (20 coupons)")
    
    coupons = generate_coupons_logic(431201, 431220)
    total = sum(coupon['valeur'] for coupon in coupons)
    
    assert len(coupons) == 20, f"Attendu: 20 coupons, Obtenu: {len(coupons)}"
    assert total == 2500, f"Attendu: 2500 DH, Obtenu: {total} DH"
    
    print(f"✅ Succès : {len(coupons)} coupons générés, Total: {total} DH")
    return True


def test_scenario_3_pages():
    """Test : Génération de 3 pages (60 coupons = 7500 DH)"""
    print("🧪 Test 2 : Génération de 3 pages (60 coupons)")
    
    coupons = generate_coupons_logic(431201, 431260)
    total = sum(coupon['valeur'] for coupon in coupons)
    
    assert len(coupons) == 60, f"Attendu: 60 coupons, Obtenu: {len(coupons)}"
    assert total == 7500, f"Attendu: 7500 DH, Obtenu: {total} DH"
    
    print(f"✅ Succès : {len(coupons)} coupons générés, Total: {total} DH")
    return True


def test_scenario_10_pages():
    """Test : Génération de 10 pages (200 coupons = 25000 DH)"""
    print("🧪 Test 3 : Génération de 10 pages (200 coupons)")
    
    coupons = generate_coupons_logic(431201, 431400)
    total = sum(coupon['valeur'] for coupon in coupons)
    
    assert len(coupons) == 200, f"Attendu: 200 coupons, Obtenu: {len(coupons)}"
    assert total == 25000, f"Attendu: 25000 DH, Obtenu: {total} DH"
    
    print(f"✅ Succès : {len(coupons)} coupons générés, Total: {total} DH")
    return True


def test_coupon_format():
    """Test : Vérification du format des coupons"""
    print("🧪 Test 4 : Vérification du format des coupons")
    
    coupons = generate_coupons_logic(431201, 431205)
    
    for coupon in coupons:
        serial = coupon['serial_number']
        assert serial.startswith('C2500'), f"Le coupon {serial} ne commence pas par C2500"
        assert 'AG' in serial, f"Le coupon {serial} ne contient pas AG"
        assert len(serial) == 16, f"Le coupon {serial} n'a pas la bonne longueur (16 caractères)"
    
    print(f"✅ Succès : Format validé pour {len(coupons)} coupons")
    return True


def test_web_interface():
    """Test : Interface web"""
    print("🧪 Test 5 : Interface web")
    
    client = Client()
    
    # Test de la page principale
    response = client.get(reverse('coupons:index'))
    assert response.status_code == 200, f"Page principale inaccessible: {response.status_code}"
    
    # Test du calcul AJAX
    response = client.post(reverse('coupons:calculate'), {
        'start_serial': 431201,
        'end_serial': 431220
    })
    assert response.status_code == 200, f"Calcul AJAX échoué: {response.status_code}"
    
    data = response.json()
    assert data['total_amount'] == 2500, f"Calcul incorrect: {data['total_amount']}"
    assert data['count'] == 20, f"Nombre incorrect: {data['count']}"
    
    print("✅ Succès : Interface web fonctionnelle")
    return True


def test_edge_cases():
    """Test : Cas limites"""
    print("🧪 Test 6 : Cas limites")
    
    # Test avec un seul coupon
    coupons = generate_coupons_logic(431201, 431201)
    assert len(coupons) == 1, f"Un seul coupon attendu, obtenu: {len(coupons)}"
    assert coupons[0]['valeur'] == 20, f"Première valeur doit être 20, obtenu: {coupons[0]['valeur']}"
    
    # Test avec plage invalide (sera géré par les vues)
    print("✅ Succès : Cas limites validés")
    return True


def main():
    """Fonction principale de test"""
    print("🚀 Démarrage des tests automatisés de l'application de gestion de coupons")
    print("=" * 70)
    
    tests = [
        test_scenario_1_page,
        test_scenario_3_pages,
        test_scenario_10_pages,
        test_coupon_format,
        test_web_interface,
        test_edge_cases
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Échec : {test.__name__} - {str(e)}")
            failed += 1
        print()
    
    print("=" * 70)
    print(f"📊 Résultats des tests :")
    print(f"✅ Tests réussis : {passed}")
    print(f"❌ Tests échoués : {failed}")
    print(f"📈 Taux de réussite : {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("🎉 Tous les tests sont passés avec succès !")
        print("🎯 L'application respecte entièrement le cahier des charges.")
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez l'implémentation.")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
