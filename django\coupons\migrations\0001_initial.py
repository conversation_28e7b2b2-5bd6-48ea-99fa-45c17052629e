# Generated by Django 5.2.4 on 2025-07-17 11:02

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Coupon',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('serial_number', models.Char<PERSON>ield(max_length=20, unique=True, verbose_name='Numéro de série')),
                ('valeur', models.IntegerField(choices=[(20, '020'), (50, '050'), (100, '100'), (200, '200'), (500, '500')], verbose_name='Valeur en DH')),
                ('suffixe', models.CharField(max_length=6, verbose_name='Suffixe')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date de création')),
            ],
            options={
                'verbose_name': 'Coupon',
                'verbose_name_plural': 'Coupons',
                'ordering': ['suffixe'],
            },
        ),
    ]
