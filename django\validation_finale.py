#!/usr/bin/env python
"""
Script de validation finale de l'application de gestion de coupons.
Vérifie que tous les composants fonctionnent correctement.
"""

import os
import sys
import django
import json
from django.test import Client
from django.urls import reverse

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'coupon_management.settings')
django.setup()

from coupons.models import Coupon


def validate_cahier_des_charges():
    """Validation complète du cahier des charges"""
    print("📋 VALIDATION DU CAHIER DES CHARGES")
    print("=" * 50)

    # Nettoyer la base de données
    Coupon.objects.all().delete()
    print("🧹 Base de données nettoyée pour la validation")

    validations = []

    # 1. Format des coupons
    print("\n🔍 1. Vérification du format C2500[VAL]AG[SUFFIXE]")
    coupon = Coupon.objects.create(
        serial_number="C2500020AG431201",
        valeur=20,
        suffixe="431201"
    )
    
    format_ok = (
        coupon.formatted_serial.startswith("C2500") and
        "AG" in coupon.formatted_serial and
        len(coupon.formatted_serial) == 16
    )
    validations.append(("Format des coupons", format_ok))
    print(f"   {'✅' if format_ok else '❌'} Format: {coupon.formatted_serial}")
    
    # 2. Valeurs autorisées
    print("\n🔍 2. Vérification des valeurs autorisées")
    valeurs_attendues = [20, 50, 100, 200, 500]
    valeurs_model = [choice[0] for choice in Coupon.VALEUR_CHOICES]
    valeurs_ok = set(valeurs_attendues) == set(valeurs_model)
    validations.append(("Valeurs autorisées", valeurs_ok))
    print(f"   {'✅' if valeurs_ok else '❌'} Valeurs: {valeurs_model}")
    
    # 3. Structure d'une page (20 coupons = 2500 DH)
    print("\n🔍 3. Vérification structure d'une page")
    from coupons.views import generate_coupons_logic
    page_coupons = generate_coupons_logic(431201, 431220)
    page_total = sum(c['valeur'] for c in page_coupons)
    page_ok = len(page_coupons) == 20 and page_total == 2500
    validations.append(("Structure page", page_ok))
    print(f"   {'✅' if page_ok else '❌'} Page: {len(page_coupons)} coupons, {page_total} DH")
    
    # 4. Structure d'un livre (10 pages = 25000 DH)
    print("\n🔍 4. Vérification structure d'un livre")
    livre_coupons = generate_coupons_logic(431201, 431400)
    livre_total = sum(c['valeur'] for c in livre_coupons)
    livre_ok = len(livre_coupons) == 200 and livre_total == 25000
    validations.append(("Structure livre", livre_ok))
    print(f"   {'✅' if livre_ok else '❌'} Livre: {len(livre_coupons)} coupons, {livre_total} DH")
    
    # 5. Modèle répétitif
    print("\n🔍 5. Vérification du modèle répétitif")
    pattern_attendu = [20, 50, 100, 200, 20, 50, 100, 200, 20, 50, 100, 200, 20, 50, 100, 500, 20, 100, 100, 500]
    pattern_obtenu = [c['valeur'] for c in page_coupons]
    pattern_ok = pattern_attendu == pattern_obtenu
    validations.append(("Modèle répétitif", pattern_ok))
    print(f"   {'✅' if pattern_ok else '❌'} Pattern: {'Conforme' if pattern_ok else 'Non conforme'}")
    
    return validations


def validate_fonctionnalites():
    """Validation des fonctionnalités"""
    print("\n🎯 VALIDATION DES FONCTIONNALITÉS")
    print("=" * 50)
    
    validations = []
    client = Client()
    
    # 1. Interface principale
    print("🔍 1. Interface principale")
    response = client.get(reverse('coupons:index'))
    interface_ok = response.status_code == 200 and "Générateur de Coupons" in response.content.decode()
    validations.append(("Interface principale", interface_ok))
    print(f"   {'✅' if interface_ok else '❌'} Page principale accessible")
    
    # 2. Génération de coupons
    print("\n🔍 2. Génération de coupons")
    Coupon.objects.all().delete()  # Nettoyer
    response = client.post(reverse('coupons:generate'), {
        'start_serial': 431201,
        'end_serial': 431220
    })
    generation_ok = response.status_code == 200 and Coupon.objects.count() == 20
    validations.append(("Génération coupons", generation_ok))
    print(f"   {'✅' if generation_ok else '❌'} Génération: {Coupon.objects.count()} coupons créés")
    
    # 3. Calcul de total (méthode algorithmique)
    print("\n🔍 3. Calcul de total (algorithmique)")
    response = client.post(reverse('coupons:calculate'), {
        'start_serial': 431201,
        'end_serial': 431210
    })
    if response.status_code == 200:
        data = response.json()
        calcul_ok = data.get('total_amount') == 810 and data.get('count') == 10
    else:
        calcul_ok = False
    validations.append(("Calcul algorithmique", calcul_ok))
    print(f"   {'✅' if calcul_ok else '❌'} Calcul: {data.get('total_amount', 'N/A')} DH")
    
    # 4. Calcul basé sur JSON/DB
    print("\n🔍 4. Calcul basé sur base de données")
    response = client.post(reverse('coupons:calculate_json'), {
        'start_serial': 'C2500020AG431201',
        'end_serial': 'C2500100AG431210'
    })
    if response.status_code == 200:
        data = response.json()
        calcul_json_ok = data.get('total_amount') == 810 and data.get('count') == 10
    else:
        calcul_json_ok = False
    validations.append(("Calcul JSON/DB", calcul_json_ok))
    print(f"   {'✅' if calcul_json_ok else '❌'} Calcul JSON: {data.get('total_amount', 'N/A')} DH")
    
    # 5. Export JSON
    print("\n🔍 5. Export JSON")
    response = client.post(reverse('coupons:export'), {
        'start_serial': 431201,
        'end_serial': 431205
    })
    export_ok = response.status_code == 200 and response['Content-Type'] == 'application/json'
    validations.append(("Export JSON", export_ok))
    print(f"   {'✅' if export_ok else '❌'} Export: {'Fonctionnel' if export_ok else 'Erreur'}")
    
    return validations


def validate_tests():
    """Validation des tests"""
    print("\n🧪 VALIDATION DES TESTS")
    print("=" * 50)
    
    # Lancer les tests Django
    from django.test.utils import get_runner
    from django.conf import settings
    
    test_runner = get_runner(settings)()
    failures = test_runner.run_tests(['coupons'])
    
    tests_ok = failures == 0
    print(f"   {'✅' if tests_ok else '❌'} Tests Django: {'Tous passés' if tests_ok else f'{failures} échecs'}")
    
    return [("Tests unitaires", tests_ok)]


def generate_rapport_final(all_validations):
    """Génère le rapport final"""
    print("\n📊 RAPPORT FINAL DE VALIDATION")
    print("=" * 50)
    
    total_tests = len(all_validations)
    tests_reussis = sum(1 for _, ok in all_validations if ok)
    pourcentage = (tests_reussis / total_tests) * 100
    
    print(f"📈 Résultats globaux:")
    print(f"   Total des validations: {total_tests}")
    print(f"   Validations réussies: {tests_reussis}")
    print(f"   Validations échouées: {total_tests - tests_reussis}")
    print(f"   Taux de réussite: {pourcentage:.1f}%")
    
    print(f"\n📋 Détail des validations:")
    for nom, resultat in all_validations:
        status = "✅ PASS" if resultat else "❌ FAIL"
        print(f"   {status} {nom}")
    
    if pourcentage == 100:
        print(f"\n🎉 VALIDATION COMPLÈTE RÉUSSIE !")
        print(f"🏆 L'application respecte entièrement le cahier des charges.")
        print(f"🚀 L'application est prête pour la production.")
    else:
        print(f"\n⚠️ Validation incomplète ({pourcentage:.1f}%)")
        print(f"🔧 Certains points nécessitent une attention.")
    
    return pourcentage == 100


def main():
    """Fonction principale de validation"""
    print("🔍 VALIDATION FINALE - Application de Gestion de Coupons")
    print("=" * 70)
    print("Cette validation vérifie la conformité complète au cahier des charges.")
    print()
    
    try:
        # Collecter toutes les validations
        all_validations = []
        
        # 1. Cahier des charges
        all_validations.extend(validate_cahier_des_charges())
        
        # 2. Fonctionnalités
        all_validations.extend(validate_fonctionnalites())
        
        # 3. Tests
        all_validations.extend(validate_tests())
        
        # 4. Rapport final
        success = generate_rapport_final(all_validations)
        
        if success:
            print(f"\n🌐 Application disponible à: http://127.0.0.1:8000/")
            print(f"🔧 Administration à: http://127.0.0.1:8000/admin/")
            print(f"📚 Documentation: README.md et DOCUMENTATION_FINALE.md")
        
        return success
        
    except Exception as e:
        print(f"\n❌ Erreur pendant la validation: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
