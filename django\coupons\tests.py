from django.test import TestCase, Client
from django.urls import reverse
from .models import Coupon
from .views import generate_coupons_logic


class CouponModelTest(TestCase):
    def test_coupon_creation(self):
        """Test de création d'un coupon"""
        coupon = Coupon.objects.create(
            serial_number="C2500020AG431201",
            valeur=20,
            suffixe="431201"
        )
        self.assertEqual(coupon.formatted_serial, "C2500020AG431201")
        self.assertEqual(coupon.valeur, 20)
        self.assertEqual(str(coupon), "C2500020AG431201 - 20 DH")

    def test_coupon_to_dict(self):
        """Test de conversion en dictionnaire"""
        coupon = Coupon.objects.create(
            serial_number="C2500500AG431220",
            valeur=500,
            suffixe="431220"
        )
        coupon_dict = coupon.to_dict()
        self.assertEqual(coupon_dict['serial_number'], "C2500500AG431220")
        self.assertEqual(coupon_dict['valeur'], 500)


class CouponLogicTest(TestCase):
    def test_generate_one_page(self):
        """Test de génération d'une page (20 coupons = 2500 DH)"""
        coupons = generate_coupons_logic(431201, 431220)
        self.assertEqual(len(coupons), 20)

        # Vérifier le total
        total = sum(coupon['valeur'] for coupon in coupons)
        self.assertEqual(total, 2500)

        # Vérifier le premier coupon
        self.assertEqual(coupons[0]['valeur'], 20)
        self.assertEqual(coupons[0]['suffixe'], "431201")
        self.assertEqual(coupons[0]['serial_number'], "C2500020AG431201")

    def test_generate_three_pages(self):
        """Test de génération de 3 pages (60 coupons = 7500 DH)"""
        coupons = generate_coupons_logic(431201, 431260)
        self.assertEqual(len(coupons), 60)

        # Vérifier le total
        total = sum(coupon['valeur'] for coupon in coupons)
        self.assertEqual(total, 7500)

    def test_generate_ten_pages(self):
        """Test de génération de 10 pages (200 coupons = 25000 DH)"""
        coupons = generate_coupons_logic(431201, 431400)
        self.assertEqual(len(coupons), 200)

        # Vérifier le total
        total = sum(coupon['valeur'] for coupon in coupons)
        self.assertEqual(total, 25000)


class CouponViewTest(TestCase):
    def setUp(self):
        self.client = Client()

    def test_index_view(self):
        """Test de la vue principale"""
        response = self.client.get(reverse('coupons:index'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Générateur de Coupons")

    def test_calculate_total_ajax(self):
        """Test du calcul de total via AJAX"""
        response = self.client.post(reverse('coupons:calculate'), {
            'start_serial': 431201,
            'end_serial': 431220
        })
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data['total_amount'], 2500)
        self.assertEqual(data['count'], 20)

    def test_generate_coupons_view(self):
        """Test de génération de coupons"""
        response = self.client.post(reverse('coupons:generate'), {
            'start_serial': 431201,
            'end_serial': 431220  # 20 coupons (multiple de 20)
        })
        self.assertEqual(response.status_code, 200)

        # Vérifier que les coupons ont été créés
        self.assertEqual(Coupon.objects.count(), 20)
