from django.contrib import admin
from .models import Coupon


@admin.register(Coupon)
class CouponAdmin(admin.ModelAdmin):
    list_display = ('formatted_serial', 'valeur', 'suffixe', 'created_at')
    list_filter = ('valeur', 'created_at')
    search_fields = ('serial_number', 'suffixe')
    readonly_fields = ('created_at',)
    ordering = ('suffixe',)

    def formatted_serial(self, obj):
        return obj.formatted_serial
    formatted_serial.short_description = 'Numéro de série'
