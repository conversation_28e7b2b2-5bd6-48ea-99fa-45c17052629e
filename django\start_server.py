#!/usr/bin/env python
"""
Script pour démarrer le serveur Django proprement
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

if __name__ == '__main__':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'coupon_management.settings')
    
    print("🚀 Démarrage du serveur Django...")
    print("📱 L'application sera disponible à: http://127.0.0.1:8000/")
    print("🛑 Pour arrêter: Ctrl+C")
    print("-" * 50)
    
    try:
        execute_from_command_line(['manage.py', 'runserver'])
    except KeyboardInterrupt:
        print("\n🛑 Serveur arrêté.")
        sys.exit(0)
