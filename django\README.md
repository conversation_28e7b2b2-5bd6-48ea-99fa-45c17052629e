# 🎫 Application de Gestion de Coupons

## 📋 Description

Application Django pour la génération et la gestion de coupons financiers selon un modèle spécifique.

### Format des Coupons
- **Modèle** : `C2500[VAL]AG[SUFFIXE]`
- **Exemple** : `C2500020AG431201` (20 DH, suffixe 431201)

### Structure d'un Livre
- **1 Page** : 20 coupons = 2500 DH
- **1 Livre** : 10 pages = 25 000 DH

## 🚀 Installation et Démarrage

### Prérequis
- Python 3.8+
- Django 5.2+

### Installation
```bash
# Cloner le projet
cd django

# Installer les dépendances
pip install django

# Effectuer les migrations
python manage.py migrate

# Créer un superutilisateur (optionnel)
python manage.py createsuperuser

# Lancer le serveur
python manage.py runserver
```

### Accès à l'application
- **Interface principale** : http://127.0.0.1:8000/
- **Administration** : http://127.0.0.1:8000/admin/

## 🎯 Fonctionnalités

### 1. Génération de Coupons
- Saisie des numéros de série de début et fin
- Génération automatique selon le modèle de page
- Sauvegarde en base de données

### 2. Calcul de Total
- Calcul instantané du montant total
- Affichage du nombre de coupons
- Calcul du nombre de pages

### 3. Export JSON
- Export des coupons générés
- Format JSON structuré avec métadonnées

## 📊 Logique de Génération

### Modèle d'une Page (20 coupons)
```
Ligne 1: 20, 50, 100, 200 DH
Ligne 2: 20, 50, 100, 200 DH  
Ligne 3: 20, 50, 100, 200 DH
Ligne 4: 20, 50, 100, 500 DH
Ligne 5: 20, 100, 100, 500 DH
Total: 2500 DH
```

### Exemples d'Utilisation
- **1 Page** : 431201-431220 = 2500 DH
- **3 Pages** : 431201-431260 = 7500 DH  
- **10 Pages** : 431201-431400 = 25 000 DH

## 🧪 Tests

```bash
# Lancer tous les tests
python manage.py test

# Tests spécifiques
python manage.py test coupons.tests.CouponLogicTest
```

### Scénarios de Test
- ✅ Génération d'une page (20 coupons)
- ✅ Génération de 3 pages (60 coupons)
- ✅ Génération de 10 pages (200 coupons)
- ✅ Calcul des totaux
- ✅ Interface utilisateur

## 📁 Structure du Projet

```
django/
├── coupon_management/          # Configuration Django
├── coupons/                    # Application principale
│   ├── models.py              # Modèle Coupon
│   ├── views.py               # Logique métier
│   ├── urls.py                # Routes
│   ├── admin.py               # Interface admin
│   ├── tests.py               # Tests unitaires
│   ├── templates/             # Templates HTML
│   └── templatetags/          # Filtres personnalisés
├── manage.py                  # Script Django
└── README.md                  # Documentation
```

## 🔧 API Endpoints

- `GET /` - Interface principale
- `POST /generate/` - Génération de coupons
- `POST /calculate/` - Calcul de total (AJAX)
- `POST /export/` - Export JSON

## 📈 Exemples de Résultats

### Export JSON
```json
{
  "metadata": {
    "start_serial": 431201,
    "end_serial": 431220,
    "total_coupons": 20,
    "total_amount": 2500
  },
  "coupons": [
    {
      "serial_number": "C2500020AG431201",
      "valeur": 20,
      "suffixe": "431201"
    }
  ]
}
```

## 👨‍💻 Développement

### Modèle de Données
```python
class Coupon(models.Model):
    serial_number = models.CharField(max_length=20, unique=True)
    valeur = models.IntegerField(choices=VALEUR_CHOICES)
    suffixe = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)
```

### Logique de Génération
La fonction `generate_coupons_logic()` implémente le modèle de page répétitif avec les valeurs dans l'ordre spécifié.

## 📞 Support

Pour toute question ou problème, consultez les tests unitaires qui documentent le comportement attendu de l'application.
