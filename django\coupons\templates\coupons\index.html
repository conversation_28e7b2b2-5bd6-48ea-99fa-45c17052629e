{% extends 'coupons/base.html' %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="mb-0">🎯 Générateur de Coupons</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5>📘 Informations sur le modèle de coupons :</h5>
                    <ul class="mb-0">
                        <li><strong>Format :</strong> C2500[VAL]AG[SUFFIXE]</li>
                        <li><strong>Une page :</strong> 20 coupons = 2500 DH</li>
                        <li><strong>Un livre :</strong> 10 pages = 25 000 DH</li>
                        <li><strong>Valeurs :</strong> 20, 50, 100, 200, 500 DH</li>
                    </ul>
                </div>

                <form method="post" action="{% url 'coupons:generate' %}" id="couponForm">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_serial" class="form-label">Numéro de série de départ</label>
                                <input type="number" class="form-control" id="start_serial" name="start_serial" 
                                       placeholder="Ex: 431201" min="1" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_serial" class="form-label">Numéro de série de fin</label>
                                <input type="number" class="form-control" id="end_serial" name="end_serial" 
                                       placeholder="Ex: 431220" min="1" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary btn-lg w-100">
                                🎫 Générer Coupons
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-success btn-lg w-100" onclick="calculateTotal()">
                                💰 Calculer Total
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-info btn-lg w-100" onclick="calculateFromJSON()">
                                📊 Calculer (JSON)
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button type="button" class="btn btn-warning btn-lg w-100" onclick="exportJSON()">
                                📁 Exporter JSON
                            </button>
                        </div>
                    </div>
                </form>

                <!-- Zone d'affichage des résultats -->
                <div id="results" class="mt-4" style="display: none;">
                    <div class="alert alert-success">
                        <h5>📊 Résultats du calcul :</h5>
                        <div id="resultsContent"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exemples -->
        <div class="card mt-4 shadow">
            <div class="card-header bg-secondary text-white">
                <h4 class="mb-0">📋 Exemples d'utilisation</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>🎫 1 Page (20 coupons)</h6>
                        <p><strong>Début :</strong> 431201<br>
                        <strong>Fin :</strong> 431220<br>
                        <strong>Total :</strong> 2500 DH</p>
                    </div>
                    <div class="col-md-4">
                        <h6>📚 1 Livre (200 coupons)</h6>
                        <p><strong>Début :</strong> 431201<br>
                        <strong>Fin :</strong> 431400<br>
                        <strong>Total :</strong> 25 000 DH</p>
                    </div>
                    <div class="col-md-4">
                        <h6>🎯 Exemple personnalisé</h6>
                        <p><strong>Début :</strong> 431201<br>
                        <strong>Fin :</strong> 431260<br>
                        <strong>Total :</strong> 7500 DH (3 pages)</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function calculateTotal() {
    const startSerial = document.getElementById('start_serial').value;
    const endSerial = document.getElementById('end_serial').value;
    
    if (!startSerial || !endSerial) {
        alert('Veuillez saisir les numéros de série de début et de fin.');
        return;
    }
    
    const formData = new FormData();
    formData.append('start_serial', startSerial);
    formData.append('end_serial', endSerial);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
    
    fetch('{% url "coupons:calculate" %}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Erreur: ' + data.error);
        } else {
            document.getElementById('resultsContent').innerHTML = `
                <strong>Plage:</strong> ${data.start_serial} - ${data.end_serial}<br>
                <strong>Nombre de coupons:</strong> ${data.count}<br>
                <strong>Montant total:</strong> ${data.total_amount} DH
            `;
            document.getElementById('results').style.display = 'block';
        }
    })
    .catch(error => {
        alert('Erreur lors du calcul: ' + error);
    });
}

function calculateFromJSON() {
    const startSerial = document.getElementById('start_serial').value;
    const endSerial = document.getElementById('end_serial').value;

    if (!startSerial || !endSerial) {
        alert('Veuillez saisir les numéros de série de début et de fin.');
        return;
    }

    const formData = new FormData();
    formData.append('start_serial', startSerial);
    formData.append('end_serial', endSerial);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch('{% url "coupons:calculate_json" %}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Erreur: ' + data.error);
        } else {
            document.getElementById('resultsContent').innerHTML = `
                <strong>Méthode:</strong> Calcul basé sur JSON (comme Tkinter)<br>
                <strong>Plage:</strong> ${data.start_suffix} - ${data.end_suffix}<br>
                <strong>Nombre de coupons:</strong> ${data.count}<br>
                <strong>Montant total:</strong> ${data.total_amount} DH
            `;
            document.getElementById('results').style.display = 'block';
        }
    })
    .catch(error => {
        alert('Erreur lors du calcul: ' + error);
    });
}

function exportJSON() {
    const startSerial = document.getElementById('start_serial').value;
    const endSerial = document.getElementById('end_serial').value;
    
    if (!startSerial || !endSerial) {
        alert('Veuillez saisir les numéros de série de début et de fin.');
        return;
    }
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{% url "coupons:export" %}';
    
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = 'csrfmiddlewaretoken';
    csrfToken.value = document.querySelector('[name=csrfmiddlewaretoken]').value;
    
    const startInput = document.createElement('input');
    startInput.type = 'hidden';
    startInput.name = 'start_serial';
    startInput.value = startSerial;
    
    const endInput = document.createElement('input');
    endInput.type = 'hidden';
    endInput.name = 'end_serial';
    endInput.value = endSerial;
    
    form.appendChild(csrfToken);
    form.appendChild(startInput);
    form.appendChild(endInput);
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}
</script>
{% endblock %}
