from django import template

register = template.Library()

@register.filter
def multiply(value, arg):
    """Multiplie deux valeurs"""
    try:
        return int(value) * int(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def divide(value, arg):
    """Divise deux valeurs"""
    try:
        return int(value) // int(arg)
    except (ValueError, TypeError, ZeroDivisionError):
        return 0

@register.filter
def pages_count(coupon_count):
    """Calcule le nombre de pages à partir du nombre de coupons"""
    try:
        return (int(coupon_count) + 19) // 20
    except (ValueError, TypeError):
        return 0
